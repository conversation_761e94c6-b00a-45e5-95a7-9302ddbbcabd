import React, {useState, useEffect} from 'react';
import HeaderView from '../components/HeaderView/index';
import {RootStackParamList} from '../navigation/AppNavigator';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {TopSafeAreaView} from '../components/CustomSafeAreaView/index';
import {VenueService} from '../services/api/APIServices';
import {View, FlatList, Text} from 'react-native';
import {BarDetails} from '../types/BarDetails';
import StorageHelper from '../utils/storageHelper';
import VenueCardView from '../components/VenueCardView/index';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen: React.FC<Props> = ({navigation}) => {
  const [arrBarList, setArrBarList] = useState<BarDetails[]>([]);

  const barlistWSCall = async () => {
    try {
      const response = await VenueService.getBarList({
        latitude: '-33.647778',
        longitude: '115.345833',
        search: '',
        showAll: 1,
        page: 1,
        serviceType: 'BOTH',
        distanceKm: 50,
      });
      if (response.success && response.data) {
        setArrBarList(response.data.barlist);
        StorageHelper.saveArray('barList', response.data.barlist);
      }
    } catch (error) {
      console.log('response', error);
    }
  };

  useEffect(() => {
    StorageHelper.getArray('barList').then(result => {
      if (result.success && result.data.length > 0) {
        setArrBarList(result.data);
        console.log('barlist', result.data);
      } else {
        barlistWSCall();
      }
    });
  }, []);
  return (
    <>
      <TopSafeAreaView />
      <HeaderView />
      <View style={{backgroundColor: '#000000', flex: 1}}>
        <FlatList
          style={{backgroundColor: '#000000'}}
          data={arrBarList}
          renderItem={({item}) => <VenueCardView item={item} />}
          keyExtractor={item => item.id.toString()}
          ListEmptyComponent={() => (
            <Text style={{color: 'white', textAlign: 'center', marginTop: 20}}>
              No venues available.
            </Text>
          )}
        />
      </View>
    </>
  );
};

export default HomeScreen;
