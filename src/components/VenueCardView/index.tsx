import {View, Image, Text} from 'react-native';
import {BarDetails} from '../../types/BarDetails';
import React from 'react';
import {VenueCardViewStyles} from './styles';

interface VenueCardViewProps {
  item: BarDetails;
}

const VenueCardView: React.FC<VenueCardViewProps> = ({item}) => {
  return (
    <View style={VenueCardViewStyles.cardStyle}>
      <Image
        style={VenueCardViewStyles.imageStyle}
        source={{uri: item.avatar}}
      />
      <Text style={VenueCardViewStyles.barTitleStyle}>
        {item.restaurantName}
      </Text>
      <Text style={VenueCardViewStyles.serviceTypeStyle}>
        {item.serviceType}
      </Text>
      <Text style={VenueCardViewStyles.totalOrdersStyle}>
        {`${item.totalOrders} orders`}
      </Text>
    </View>
  );
};
export default VenueCardView;
