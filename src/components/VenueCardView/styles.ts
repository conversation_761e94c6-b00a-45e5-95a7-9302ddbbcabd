import {StyleSheet} from 'react-native';
import Colors from '../../utils/colors';
import {FontSize} from '../../utils/fonts';

export const VenueCardViewStyles = StyleSheet.create({
  cardStyle: {
    backgroundColor: '#fff',
    shadowColor: Colors.app_black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    marginBottom: 10,
  },

  imageStyle: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
    alignContent: 'center',
  },

  barTitleStyle: {
    fontFamily: 'BalooBhai2-Medium',
    color: Colors.app_black,
    fontSize: FontSize.size_20,
    marginVertical: 10,
    textAlign: 'left',
    padding: 10,
  },

  serviceTypeStyle: {
    fontFamily: 'BalooBhai2-Regular',
    color: Colors.app_black_50,
    fontSize: FontSize.size_17,
    marginVertical: 10,
    textAlign: 'left',
    padding: 10,
  },

  totalOrdersStyle: {
    fontFamily: 'BalooBhai2-Regular',
    color: Colors.app_black,
    fontSize: FontSize.size_15,
    marginVertical: 10,
    textAlign: 'left',
    padding: 10,
  },

  distanceStyle: {
    fontFamily: 'BalooBhai2-Regular',
    color: Colors.app_black,
    fontSize: FontSize.size_16,
    marginVertical: 10,
    textAlign: 'right',
    padding: 10,
  },
});
